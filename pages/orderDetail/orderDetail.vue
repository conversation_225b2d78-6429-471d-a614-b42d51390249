<template>
  <view class="container" v-if="order">
    <!-- 顶部操作栏 -->
    <view class="action-bar">
      <view class="status-tag" :class="`status-${order.statusCode}`">{{ order.status }}</view>
    </view>

    <!-- 基本信息卡片 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">基本信息</text>
      </view>
      <view class="card-body">
        <view class="form-item">
          <text class="label">申请单号</text>
          <text class="value highlight">{{ order.id }}</text>
        </view>
        <view class="form-item">
          <text class="label">申请人</text>
          <text class="value">{{ order.applicantName }}</text>
        </view>
        <view class="form-item">
          <text class="label">工序</text>
          <text class="value">{{ order.process }}</text>
        </view>
        <view class="form-item">
          <text class="label">装置</text>
          <text class="value">{{ order.device }}</text>
        </view>
        <view class="form-item">
          <text class="label">组织</text>
          <text class="value">{{ order.organization }}</text>
        </view>
        <view class="form-item">
          <text class="label">申请日期</text>
          <text class="value">{{ order.applicationDate }}</text>
        </view>
      </view>
    </view>

    <!-- 固废转移卡片 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">固废转移</text>
      </view>
      <view class="card-body">
        <view class="form-item">
          <text class="label">申请类型</text>
          <text class="value">{{ order.applicationType }}</text>
        </view>
        <view class="form-item">
          <text class="label">是否计划内</text>
          <text class="value">{{ order.isPlan }}</text>
        </view>
        <view class="form-item">
          <text class="label">处置去向</text>
          <text class="value">{{ order.disposalDestination }}</text>
        </view>
        <view class="form-item">
          <text class="label">联系电话</text>
          <text class="value">{{ order.contactPhone }}</text>
        </view>
        <view class="form-item">
          <text class="label">预计转移量</text>
          <text class="value highlight">{{ order.estimatedTransferAmount }}</text>
        </view>
        <view class="form-item">
          <text class="label">预计转移时间</text>
          <text class="value">{{ order.estimatedTransferTime }}</text>
        </view>
        <view class="form-item">
          <text class="label">实际转移时间</text>
          <text class="value">{{ order.actualTransferTime }}</text>
        </view>
        <view class="form-item">
          <text class="label">转移联系人</text>
          <text class="value">{{ order.transferContact }}</text>
        </view>
        <view class="form-item">
          <text class="label">实际操作人</text>
          <text class="value">{{ order.actualOperator }}</text>
        </view>
        <view class="form-item">
          <text class="label">危废产生位置</text>
          <text class="value">{{ order.wasteLocation }}</text>
        </view>
      </view>
    </view>

    <!-- 固废信息卡片 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">固废信息</text>
      </view>
      <view class="card-body">
        <view class="form-item">
          <text class="label">固废名称</text>
          <text class="value highlight">{{ order.wasteName }}</text>
        </view>
        <view class="form-item">
          <text class="label">固废代码</text>
          <text class="value">{{ order.wasteCode }}</text>
        </view>
        <view class="form-item">
          <text class="label">固废类别</text>
          <text class="value">{{ order.wasteCategory }}</text>
        </view>
        <view class="form-item">
          <text class="label">报批分组</text>
          <text class="value">{{ order.approvalGroup }}</text>
        </view>
        <view class="form-item">
          <text class="label">危险特性</text>
          <text class="value">{{ order.dangerCharacteristics }}</text>
        </view>
        <view class="form-item">
          <text class="label">固废形态</text>
          <text class="value">{{ order.wasteForm }}</text>
        </view>
        <view class="form-item">
          <text class="label">主要有害成分</text>
          <text class="value">{{ order.mainHarmfulComponents }}</text>
        </view>
        <view class="form-item">
          <text class="label">包装方式</text>
          <text class="value">{{ order.packagingMethod }}</text>
        </view>
        <view class="form-item">
          <text class="label">紧急与反应措施</text>
          <text class="value" :class="order.carNumber === '未分配' ? 'empty' : 'highlight'">{{ order.emergencyMeasures }}</text>
        </view>
      </view>
    </view>

    <!-- 外运信息卡片 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">外运信息</text>
      </view>
      <view class="card-body">
        <view class="form-item">
          <text class="label">外运目的</text>
          <text class="value">{{ order.transportPurpose }}</text>
        </view>
        <view class="form-item">
          <text class="label">起点</text>
          <text class="value">{{ order.start }}</text>
        </view>
        <view class="form-item">
          <text class="label">终点</text>
          <text class="value">{{ order.end }}</text>
        </view>
        <view class="form-item">
          <text class="label">废物运输车牌号</text>
          <text class="value">{{ order.carNumber || '未分配' }}</text>
        </view>
        <view class="form-item">
          <text class="label">司机</text>
          <text class="value">{{ order.driverName || '未分配' }}</text>
        </view>
        <view class="form-item">
          <text class="label">联系电话</text>
          <text class="value">{{ order.driverPhone || '未分配' }}</text>
        </view>
      </view>
        <view class="form-item">
          <text class="label">产生企业</text>
          <text class="value">{{ order.produceEnterpriseName }}</text>
        </view>
        <view class="form-item">
          <text class="label">运输企业</text>
          <text class="value">{{ order.transferEnterpriseName }}</text>
        </view>
        <view class="form-item">
          <text class="label">接受单位</text>
          <text class="value">{{ order.disposeEnterpriseName }}</text>
        </view>
        <view class="form-item">
          <text class="label">五联单号</text>
          <text class="value">{{ order.fiveBillsCode }}</text>
        </view>
        <view class="form-item">
          <text class="label">承运人</text>
          <text class="value">{{ order.transferContact }}</text>
        </view>
        <view class="form-item">
          <text class="label">车卡</text>
          <text class="value">{{ order.cardName }}</text>
        </view>
        <view class="form-item">
          <text class="label">电子锁</text>
          <text class="value">{{ order.lockCode || '无' }}</text>
        </view>
        <view class="form-item">
          <text class="label">电子锁回收确认时间</text>
          <text class="value">{{ order.lockRecycleTime }}</text>
        </view>
        <view class="form-item">
          <text class="label">过磅单号</text>
          <text class="value">{{ order.weightRecordCode }}</text>
        </view>

    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-buttons">
      <button class="bottom-btn" @tap="contactDispatch">
        <view class="btn-content">
          <image class="btn-icon" src="/static/images/icon-phone.png"></image>
          <text>联系外运</text>
        </view>
      </button>
    </view>
  </view>

  <!-- 加载中占位 -->
  <view class="loading-container" v-else-if="loading">
    <view class="loading">加载中...</view>
  </view>

  <!-- 加载失败占位 -->
  <view class="error-container" v-else>
    <view class="error-message">订单详情加载失败</view>
    <button class="retry-btn" @tap="retryLoad">重试</button>
  </view>
</template>

<script>
import { getOrderDetail } from '@/services/orderService'

export default {
  data() {
    return {
      order: null,
      loading: true,
      orderId: null
    }
  },
  onLoad(options) {
    this.orderId = options && options.id

    // 优先使用从列表页传递的数据
    const globalData = getApp().globalData
    if (globalData && globalData.currentOrderDetail) {
      console.log('使用列表页传递的数据，避免重复请求接口')
      try {
        this.order = this.formatOrderData(globalData.currentOrderDetail)
        this.loading = false
        uni.setNavigationBarTitle({ title: '处置单详情' })

        // 清除全局数据，避免影响下次使用
        delete globalData.currentOrderDetail
      } catch (error) {
        console.error('格式化传递的数据失败:', error)
        // 如果格式化失败，降级到接口请求
        this.loadOrderDetail(this.orderId)
      }
    } else {
      console.log('全局数据不存在（可能是直接访问详情页），请求接口获取')
      this.loadOrderDetail(this.orderId)
    }
  },
  methods: {
    async loadOrderDetail(orderId) {
      if (!orderId) {
        uni.showToast({ title: '订单ID不能为空', icon: 'error' })
        return
      }

      try {
        this.loading = true
        const response = await getOrderDetail(orderId)

        if (response.code === 200 && response.data) {
          this.order = this.formatOrderData(response.data)
          uni.setNavigationBarTitle({ title: '处置单详情' })
        } else {
          uni.showToast({ title: response.message || '获取订单详情失败', icon: 'error' })
        }
      } catch (error) {
        console.error('加载订单详情失败:', error)
        uni.showToast({ title: '加载订单详情失败', icon: 'error' })
      } finally {
        this.loading = false
      }
    },

    // 格式化订单数据用于显示
    formatOrderData(rawData) {
      return {
        // 基础信息
        id: rawData.apply_code || rawData.transfer_id,
        applicantName: rawData.user_name || '未知',
        process: rawData.transfer_position || '未知',
        device: rawData.org_name || '未知',
        organization: rawData.parent_org_name || rawData.org_name || '未知',
        applicationDate: this.formatTime(rawData.apply_date),
        status: this.formatStatus(rawData.bpm_status),
        statusCode: rawData.bpm_status,

        // 固废转移信息
        applicationType: this.formatApplyType(rawData.apply_type),
        isPlan: rawData.is_plan === '1' ? '是' : '否',
        disposalDestination: this.formatDisposalType(rawData.is_sales),
        contactPhone: rawData.phone || '未知',
        estimatedTransferAmount: rawData.plan_transfer_quantity ? `${rawData.plan_transfer_quantity}吨` : '未知',
        estimatedTransferTime: this.formatTime(rawData.plan_transfer_time),
        actualTransferTime: this.formatTime(rawData.transfer_time),
        transferContact: rawData.transfer_person || '未知',
        actualOperator: rawData.operator || rawData.duty_person || '未知',
        wasteLocation: rawData.transfer_position || '未知',

        // 固废信息
        wasteName: rawData.waste_name || '未知',
        wasteCode: rawData.parent_category_code || '未知',
        wasteCategory: rawData.category_code || '未知',
        approvalGroup: rawData.report_group_name || '未知',
        dangerCharacteristics: rawData.risk || '未知',
        wasteForm: rawData.waste_modal || '未知',
        mainHarmfulComponents: rawData.harmful_ingredient || '无',
        packagingMethod: rawData.package_type || '未知',
        emergencyMeasures: rawData.security_measure || '未知',

        // 外运信息
        transportPurpose: rawData.outer_goal || '处置',
        start: rawData.transfer_start_position || '未知',
        end: rawData.transfer_end_position || '未知',
        wasteType: '危险废物',
        carNumber: rawData.car_code || '未分配',
        driverName: rawData.transfer_person || '未分配',
        driverPhone: rawData.phone || '未分配',
        contactPerson: rawData.user_name || '未知',

        // 补充外运相关单位
        produceEnterpriseName: rawData.produce_enterprise_name || rawData.produce_enterprise || '未知',
        transferEnterpriseName: rawData.transfer_enterprise_name || rawData.transfer_enterprise || '未知',
        disposeEnterpriseName: rawData.dispose_enterprise_name || rawData.dispose_enterprise || '未知',

        // 其他信息
        fiveBillsCode: rawData.five_bills_code || '无',
        cardCode: rawData.card_code || '未知',
        cardName: rawData.card_name || '未知',
        note: rawData.note || '无',
        createdTime: this.formatTime(rawData.apply_date),
        lastUpdatedTime: this.formatTime(rawData.bmp_update_time || rawData.update_time),

        // 补充的详细信息
        weightRecordCode: rawData.weight_record_code || '无',
        transferType: rawData.transfer_type || '未知',
        handleType: rawData.handle_type || '未知',
        lockCode: rawData.lock_code || rawData.lock_id || '',
        lockRecycleTime: this.formatTime(rawData.lock_recycle_time),
        waitCount: rawData.wait_count || '0',
        passCount: rawData.pass_count || '0',
        backCount: rawData.back_count || '0'
      }
    },

    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp) return '未知'
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 格式化状态
    formatStatus(bmpStatus) {
      const statusMap = {
        'finish': '审批完成',
        'contact': '联系外运',
        'out-store': '已出库',
        'weight': '已过磅',
        'print': '联单已打印',
        'upload': '联单已上传',
        'close': '已关闭',
        'cancel': '已取消'
      }
      return statusMap[bmpStatus] || bmpStatus || '未知'
    },

    // 格式化申请类型
    formatApplyType(applyType) {
      const typeMap = {
        'equ': '装置',
        'station': '固废站'
      }
      return typeMap[applyType] || applyType || '未知'
    },

    // 格式化处置去向
    formatDisposalType(disposalType) {
      const disposalMap = {
        '0': '直接外委处置',
        '1': '有价值处置',
        '2': '环保科技处置',
        '3': '无价值处置'
      }
      return disposalMap[disposalType] || disposalType || '未知'
    },

    // 重试加载
    retryLoad() {
      this.loadOrderDetail(this.orderId)
    },

    contactDispatch() {
      if (!this.order) return
      uni.navigateTo({ url: `/pages/transportForm/transportForm?id=${this.order.id}` })
    }
  }
}
</script>

<style scoped>
.container { padding: 20rpx; background-color: #f7f7f7; min-height: 100vh; }
/* 顶部操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}
.status-tag {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
/* 状态颜色 */
.status-finish { background: linear-gradient(135deg, #4caf50, #45a049); }
.status-contact { background: linear-gradient(135deg, #2196f3, #1976d2); }
.status-out-store { background: linear-gradient(135deg, #ff9800, #f57c00); }
.status-weight { background: linear-gradient(135deg, #9c27b0, #7b1fa2); }
.status-print { background: linear-gradient(135deg, #607d8b, #455a64); }
.status-upload { background: linear-gradient(135deg, #795548, #5d4037); }
.status-close { background: linear-gradient(135deg, #9e9e9e, #757575); }
.status-cancel { background: linear-gradient(135deg, #f44336, #d32f2f); }
/* 默认状态样式 */
.status-tag { background: linear-gradient(135deg, #6c757d, #495057); }
/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}
.card-header {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: flex;
  align-items: center;
}
.card-title::before {
  content: '';
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #003366, #0066cc);
  border-radius: 3rpx;
  margin-right: 12rpx;
}
.card-body {
  padding: 24rpx 32rpx;
}
/* 表单项样式 */
.form-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: flex-start;
  min-height: 60rpx;
}
.form-item:last-child { border-bottom: none; }
.label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
  line-height: 1.4;
  flex-shrink: 0;
}
.value {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  word-break: break-all;
  line-height: 1.4;
  margin-left: 20rpx;
}

/* 特殊值样式 */
.value.empty {
  color: #999999;
  font-style: italic;
}

.value.highlight {
  color: #003366;
  font-weight: 500;
}

.value.success {
  color: #4caf50;
  font-weight: 500;
}

.value.warning {
  color: #ff9800;
  font-weight: 500;
}
/* 底部操作按钮 */
.bottom-buttons { display: flex; justify-content: center; padding: 40rpx 0 60rpx; margin-top: 30rpx; }
.bottom-btn { width: 70%; background: linear-gradient(to right, #1976D2, #0D47A1); color: #ffffff; font-size: 32rpx; height: 88rpx; line-height: 88rpx; border-radius: 44rpx; box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3); letter-spacing: 2rpx; font-weight: 500; transition: all 0.3s; border: none; position: relative; overflow: hidden; padding: 0; }
.btn-content { display: flex; align-items: center; justify-content: center; height: 100%; }
.btn-icon { width: 36rpx; height: 36rpx; margin-right: 12rpx; }
.bottom-btn::after { content: ""; display: block; position: absolute; width: 100%; height: 100%; top: 0; left: 0; pointer-events: none; background-image: radial-gradient(circle, #fff 10%, transparent 10.01%); background-repeat: no-repeat; background-position: 50%; transform: scale(10, 10); opacity: 0; transition: transform 0.5s, opacity 1s; }
.bottom-btn:active::after { transform: scale(0, 0); opacity: 0.3; transition: 0s; }
/* 加载中 */
.loading-container { display: flex; justify-content: center; align-items: center; height: 100vh; }
.loading { color: #999999; font-size: 28rpx; }

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 40rpx;
}

.error-message {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: #003366;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}
</style>